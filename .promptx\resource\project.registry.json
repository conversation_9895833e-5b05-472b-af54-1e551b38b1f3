{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T11:01:06.223Z", "updatedAt": "2025-07-29T11:01:06.224Z", "resourceCount": 7}, "resources": [{"id": "chinese-history-expert", "source": "project", "protocol": "role", "name": "Chinese History Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/chinese-history-expert/chinese-history-expert.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.223Z", "updatedAt": "2025-07-29T11:01:06.223Z", "scannedAt": "2025-07-29T11:01:06.223Z", "path": "role/chinese-history-expert/chinese-history-expert.role.md"}}, {"id": "content-expansion-pm", "source": "project", "protocol": "role", "name": "Content Expansion Pm 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/content-expansion-pm/content-expansion-pm.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.224Z", "updatedAt": "2025-07-29T11:01:06.224Z", "scannedAt": "2025-07-29T11:01:06.224Z", "path": "role/content-expansion-pm/content-expansion-pm.role.md"}}, {"id": "content-planning-expert", "source": "project", "protocol": "role", "name": "Content Planning Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/content-planning-expert/content-planning-expert.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.224Z", "updatedAt": "2025-07-29T11:01:06.224Z", "scannedAt": "2025-07-29T11:01:06.224Z", "path": "role/content-planning-expert/content-planning-expert.role.md"}}, {"id": "knowledge-content-developer", "source": "project", "protocol": "role", "name": "Knowledge Content Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/knowledge-content-developer/knowledge-content-developer.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.224Z", "updatedAt": "2025-07-29T11:01:06.224Z", "scannedAt": "2025-07-29T11:01:06.224Z", "path": "role/knowledge-content-developer/knowledge-content-developer.role.md"}}, {"id": "knowledge-context-manager", "source": "project", "protocol": "role", "name": "Knowledge Context Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/knowledge-context-manager/knowledge-context-manager.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.224Z", "updatedAt": "2025-07-29T11:01:06.224Z", "scannedAt": "2025-07-29T11:01:06.224Z", "path": "role/knowledge-context-manager/knowledge-context-manager.role.md"}}, {"id": "learning-experience-analyst", "source": "project", "protocol": "role", "name": "Learning Experience Analyst 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/learning-experience-analyst/learning-experience-analyst.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.224Z", "updatedAt": "2025-07-29T11:01:06.224Z", "scannedAt": "2025-07-29T11:01:06.224Z", "path": "role/learning-experience-analyst/learning-experience-analyst.role.md"}}, {"id": "miniprogram-architect", "source": "project", "protocol": "role", "name": "Miniprogram Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/miniprogram-architect/miniprogram-architect.role.md", "metadata": {"createdAt": "2025-07-29T11:01:06.224Z", "updatedAt": "2025-07-29T11:01:06.224Z", "scannedAt": "2025-07-29T11:01:06.224Z", "path": "role/miniprogram-architect/miniprogram-architect.role.md"}}], "stats": {"totalResources": 7, "byProtocol": {"role": 7}, "bySource": {"project": 7}}}